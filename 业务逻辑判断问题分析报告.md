# 业务逻辑判断问题分析报告

## 问题描述
工作流生成的代码在业务逻辑判断时存在固定模式错误：
- 用户要求：光照强度 > 50lux 且距离 < 30cm 时触发蜂鸣器
- 实际生成：光照强度 < 100lux 且距离 < 50cm 的判断逻辑

## 根本原因分析

### 1. 模型Temperature配置问题
**文件**: `app/langgraph_def/graph_builder.py` (第47-54行)
```python
system_architect_model = ChatOpenAI(model="kimi-k2-250711", temperature=0.5, api_key=API_KEY, base_url=BASE_URL)
module_architect_model = ChatOpenAI(model="kimi-k2-250711", temperature=0.4, api_key=API_KEY, base_url=BASE_URL)
api_designer_model = ChatOpenAI(model="kimi-k2-250711", temperature=0.3, api_key=API_KEY, base_url=BASE_URL)
developer_model = ChatOpenAI(model="kimi-k2-250711", temperature=0.2, api_key=API_KEY, base_url=BASE_URL)
```

**问题**: `system_architect_model` 的temperature=0.5过高，导致描述重写时引入随机性

### 2. 描述重写节点的逻辑保护失效
**文件**: `app/langgraph_def/graph_builder.py` (第1174-1184行)

**关键规则**:
```xml
<Rule ID="CRITICAL_LOGIC_PRESERVATION" Priority="ABSOLUTE">
CRITICAL RULE: You MUST identify and preserve all specific numerical values, comparison operators (>, <, ==, etc.), and logical operators (AND, OR, NOT) from the original description.
</Rule>
```

**问题**: 尽管有明确规则，但AI模型仍可能在重写过程中改变数值

### 3. 原始需求备份机制存在缺陷
**文件**: `app/langgraph_def/graph_builder.py` (第1119-1123行)
```python
original_description = task.get('description', '')
# 核心修改：在重写描述之前，将原始的中文需求备份到新字段中
task['original_description_for_developer'] = original_description
```

**问题**: 备份发生在重写之前，但如果原始description已经被污染，备份也是错误的

### 4. 开发者节点的双重输入冲突
**文件**: `app/langgraph_def/graph_builder.py` (第2464行, 第2588-2589行)

**冲突的指令**:
```xml
1. **Source of Truth for Logic (ULTRA-CRITICAL):** The `<OriginalRequirement_SourceOfTruth>` block contains the user's original, unmodified request. This block is the **ABSOLUTE AND ONLY SOURCE OF TRUTH** for business logic
```

但同时传入了两个上下文：
```python
{original_requirement_context}  # 来自备份的原始需求
<TaskDescription>{original_description}</TaskDescription>  # 来自重写后的描述
```

**问题**: 如果两个描述不一致，AI可能选择错误的源

### 5. API包中的硬编码默认值影响
**多个API包文件中存在默认阈值**:
- `50101BS_API_Package.json`: `"default_brightness": 50`
- `SM4205_API_Package.json`: `"default_brightness": 50`
- `DS1624_API_Package.json`: `"timeout_threshold_ms": 1000`

**问题**: AI可能受到这些默认值影响，倾向于使用50、100等"常见"数值

### 6. 示例代码中的固定模式
**文件**: `app/langgraph_def/graph_builder.py` (第1198-1216行)

**示例中的固定模式**:
```xml
<OriginalDescription>接收来自大绿板的光照强度和距离数据，如果光照强度高于50lux且距离小于50cm，立即激活5号引脚的蜂鸣器</OriginalDescription>
<LogicRules>
- Activation condition: light intensity > 50 lux AND distance < 50cm
- Deactivation condition: light intensity <= 50 lux OR distance >= 50cm
</LogicRules>
```

**问题**: 示例使用了50lux和50cm，可能导致AI产生模式记忆

## 修复建议

### 1. 降低system_architect_model的temperature
```python
system_architect_model = ChatOpenAI(model="kimi-k2-250711", temperature=0.1, api_key=API_KEY, base_url=BASE_URL)
```

### 2. 在更早阶段备份原始需求
在`project_analyzer_service.py`中直接备份用户的原始输入，避免任何中间处理的污染

### 3. 增强数值验证机制
在描述重写后，添加数值一致性检查，确保关键数值未被改变

### 4. 移除或修改示例中的固定数值
将示例中的50lux、50cm改为变量形式，避免模式固化

### 5. 添加契约验证层
在开发者节点生成代码后，验证生成的条件判断是否与原始需求一致

### 6. 优化prompt结构
明确指定哪个输入源具有最高优先级，避免AI在多个输入间产生混淆
